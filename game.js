// Game variables
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const scoreElement = document.getElementById('score');
const gameOverElement = document.getElementById('gameOver');
const finalScoreElement = document.getElementById('finalScore');

// Game state
let gameRunning = true;
let gameStarted = false;
let score = 0;
let frameCount = 0;

// Audio system
let audioContext;
let backgroundAmbience;
let audioEnabled = true;
let musicVolume = 0.15;
let sfxVolume = 0.3;

// Turtle player object (swimming turtle)
const turtle = {
    x: 150,
    y: canvas.height / 2,
    width: 60,
    height: 45,
    velocityY: 0,
    swimPower: -6,
    buoyancy: 0.3, // Slower than gravity for underwater feel
    rotation: 0,
    color: '#228B22'
};

// Arrays for game objects
let obstacles = [];
let bubbles = [];
let seaweed = [];

// Game constants
const obstacleWidth = 60;
const obstacleGap = 160;
const gameSpeed = 2.5;
const spawnRate = 120; // frames between spawns

// Audio functions
function initAudio() {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        createUnderwaterAmbience();
    } catch (error) {
        console.log('Audio not supported:', error);
        audioEnabled = false;
    }
}

function createUnderwaterAmbience() {
    if (!audioContext || !audioEnabled) return;

    // Create underwater ambient sound using multiple oscillators
    const gainNode = audioContext.createGain();
    gainNode.connect(audioContext.destination);
    gainNode.gain.setValueAtTime(musicVolume, audioContext.currentTime);

    // Low frequency rumble (ocean depth)
    const lowOsc = audioContext.createOscillator();
    const lowGain = audioContext.createGain();
    lowOsc.type = 'sine';
    lowOsc.frequency.setValueAtTime(40, audioContext.currentTime);
    lowGain.gain.setValueAtTime(0.3, audioContext.currentTime);
    lowOsc.connect(lowGain);
    lowGain.connect(gainNode);

    // Mid frequency waves (water movement)
    const midOsc = audioContext.createOscillator();
    const midGain = audioContext.createGain();
    midOsc.type = 'triangle';
    midOsc.frequency.setValueAtTime(120, audioContext.currentTime);
    midGain.gain.setValueAtTime(0.2, audioContext.currentTime);
    midOsc.connect(midGain);
    midGain.connect(gainNode);

    // High frequency bubbles (subtle)
    const highOsc = audioContext.createOscillator();
    const highGain = audioContext.createGain();
    highOsc.type = 'sine';
    highOsc.frequency.setValueAtTime(800, audioContext.currentTime);
    highGain.gain.setValueAtTime(0.05, audioContext.currentTime);
    highOsc.connect(highGain);
    highGain.connect(gainNode);

    // Add some variation to make it more organic
    setInterval(() => {
        if (audioContext && audioEnabled) {
            const time = audioContext.currentTime;
            lowOsc.frequency.setValueAtTime(35 + Math.random() * 10, time);
            midOsc.frequency.setValueAtTime(100 + Math.random() * 40, time);
            highOsc.frequency.setValueAtTime(700 + Math.random() * 200, time);
        }
    }, 2000);

    // Start all oscillators
    lowOsc.start();
    midOsc.start();
    highOsc.start();

    backgroundAmbience = { lowOsc, midOsc, highOsc, gainNode };
}

function playSwimSound() {
    if (!audioContext || !audioEnabled) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Create a "whoosh" sound for swimming
    oscillator.type = 'sawtooth';
    oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.2);

    gainNode.gain.setValueAtTime(sfxVolume * 0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.2);
}

function playBubbleSound() {
    if (!audioContext || !audioEnabled) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Create a bubble pop sound
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(800 + Math.random() * 400, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.1);

    gainNode.gain.setValueAtTime(sfxVolume * 0.2, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.1);
}

function playScoreSound() {
    if (!audioContext || !audioEnabled) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Create a pleasant score sound
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(523, audioContext.currentTime); // C5
    oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1); // E5

    gainNode.gain.setValueAtTime(sfxVolume * 0.4, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.3);
}

function playGameOverSound() {
    if (!audioContext || !audioEnabled) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Create a descending game over sound
    oscillator.type = 'triangle';
    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 1);

    gainNode.gain.setValueAtTime(sfxVolume * 0.5, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + 1);
}

function toggleAudio() {
    audioEnabled = !audioEnabled;
    const toggleButton = document.getElementById('audioToggle');

    if (!audioEnabled && backgroundAmbience) {
        backgroundAmbience.gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        toggleButton.textContent = '🔇 Sound OFF';
    } else if (audioEnabled && backgroundAmbience) {
        backgroundAmbience.gainNode.gain.setValueAtTime(musicVolume, audioContext.currentTime);
        toggleButton.textContent = '🔊 Sound ON';
    }
}

// Initialize underwater elements
function initUnderwaterElements() {
    // Initialize bubbles
    for (let i = 0; i < 8; i++) {
        bubbles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            radius: 3 + Math.random() * 8,
            speed: 0.5 + Math.random() * 1.5,
            opacity: 0.3 + Math.random() * 0.4
        });
    }

    // Initialize seaweed
    for (let i = 0; i < 6; i++) {
        seaweed.push({
            x: Math.random() * canvas.width,
            height: 80 + Math.random() * 60,
            sway: Math.random() * Math.PI * 2,
            speed: 0.3 + Math.random() * 0.3
        });
    }
}

// Draw functions
function drawTurtle() {
    ctx.save();
    ctx.translate(turtle.x + turtle.width/2, turtle.y + turtle.height/2);
    ctx.rotate(turtle.rotation);

    // Turtle shell (main body)
    ctx.fillStyle = turtle.color;
    ctx.beginPath();
    ctx.ellipse(0, 0, turtle.width/2, turtle.height/2, 0, 0, 2 * Math.PI);
    ctx.fill();

    // Shell pattern
    ctx.fillStyle = '#006400';
    ctx.beginPath();
    ctx.ellipse(0, 0, turtle.width/2 - 5, turtle.height/2 - 5, 0, 0, 2 * Math.PI);
    ctx.fill();

    // Shell segments
    ctx.strokeStyle = '#228B22';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(-turtle.width/4, -turtle.height/4);
    ctx.lineTo(turtle.width/4, turtle.height/4);
    ctx.moveTo(turtle.width/4, -turtle.height/4);
    ctx.lineTo(-turtle.width/4, turtle.height/4);
    ctx.moveTo(0, -turtle.height/2 + 5);
    ctx.lineTo(0, turtle.height/2 - 5);
    ctx.stroke();

    // Turtle head
    ctx.fillStyle = '#32CD32';
    ctx.beginPath();
    ctx.ellipse(turtle.width/2 + 8, 0, 12, 8, 0, 0, 2 * Math.PI);
    ctx.fill();

    // Turtle eyes
    ctx.fillStyle = 'black';
    ctx.beginPath();
    ctx.arc(turtle.width/2 + 12, -3, 2, 0, 2 * Math.PI);
    ctx.arc(turtle.width/2 + 12, 3, 2, 0, 2 * Math.PI);
    ctx.fill();

    // Swimming flippers (animated)
    ctx.fillStyle = '#32CD32';
    let flipperMotion = Math.sin(frameCount * 0.2) * 0.3;

    // Front flippers
    ctx.save();
    ctx.rotate(flipperMotion);
    ctx.fillRect(-turtle.width/2 - 8, -turtle.height/4, 15, 8);
    ctx.fillRect(-turtle.width/2 - 8, turtle.height/4 - 8, 15, 8);
    ctx.restore();

    // Back flippers
    ctx.save();
    ctx.rotate(-flipperMotion);
    ctx.fillRect(turtle.width/2 - 7, -turtle.height/4, 12, 6);
    ctx.fillRect(turtle.width/2 - 7, turtle.height/4 - 6, 12, 6);
    ctx.restore();

    ctx.restore();
}

function drawObstacle(obstacle) {
    // Top coral reef
    ctx.fillStyle = '#FF6347';
    drawCoral(obstacle.x, 0, obstacleWidth, obstacle.topHeight);

    // Bottom coral reef
    ctx.fillStyle = '#FF4500';
    drawCoral(obstacle.x, obstacle.topHeight + obstacle.gap, obstacleWidth, canvas.height - obstacle.topHeight - obstacle.gap);
}

function drawCoral(x, y, width, height) {
    // Main coral structure
    ctx.fillRect(x + width/4, y, width/2, height);

    // Coral branches
    for (let i = 0; i < 5; i++) {
        let branchY = y + (height / 5) * i;
        let branchWidth = 8 + Math.sin(frameCount * 0.1 + i) * 3;

        // Left branch
        ctx.fillRect(x + width/4 - branchWidth, branchY, branchWidth, 12);

        // Right branch
        ctx.fillRect(x + 3*width/4, branchY, branchWidth, 12);
    }

    // Coral texture dots
    ctx.fillStyle = '#FFB347';
    for (let i = 0; i < width/8; i++) {
        for (let j = 0; j < height/15; j++) {
            if (Math.random() > 0.7) {
                ctx.fillRect(x + i * 8 + Math.random() * 8, y + j * 15 + Math.random() * 15, 2, 2);
            }
        }
    }
}

function drawBubble(bubble) {
    ctx.fillStyle = `rgba(173, 216, 230, ${bubble.opacity})`;
    ctx.strokeStyle = `rgba(255, 255, 255, ${bubble.opacity * 0.8})`;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(bubble.x, bubble.y, bubble.radius, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();

    // Bubble highlight
    ctx.fillStyle = `rgba(255, 255, 255, ${bubble.opacity * 0.6})`;
    ctx.beginPath();
    ctx.arc(bubble.x - bubble.radius/3, bubble.y - bubble.radius/3, bubble.radius/4, 0, 2 * Math.PI);
    ctx.fill();
}

function drawSeaweed(weed) {
    ctx.strokeStyle = '#228B22';
    ctx.lineWidth = 4;
    ctx.lineCap = 'round';

    let segments = 8;
    let segmentHeight = weed.height / segments;

    ctx.beginPath();
    ctx.moveTo(weed.x, canvas.height);

    for (let i = 1; i <= segments; i++) {
        let y = canvas.height - (segmentHeight * i);
        let sway = Math.sin(weed.sway + i * 0.5) * (i * 2);
        ctx.lineTo(weed.x + sway, y);
    }

    ctx.stroke();

    // Seaweed leaves
    ctx.fillStyle = '#32CD32';
    for (let i = 2; i < segments; i += 2) {
        let y = canvas.height - (segmentHeight * i);
        let sway = Math.sin(weed.sway + i * 0.5) * (i * 2);

        ctx.beginPath();
        ctx.ellipse(weed.x + sway, y, 8, 4, weed.sway + i, 0, 2 * Math.PI);
        ctx.fill();
    }
}

// Game logic functions
function updateTurtle() {
    if (!gameStarted) return;

    // Apply buoyancy (like underwater gravity)
    turtle.velocityY += turtle.buoyancy;
    turtle.y += turtle.velocityY;

    // Update rotation based on velocity (more subtle for swimming)
    turtle.rotation = Math.min(Math.max(turtle.velocityY * 0.05, -0.3), 0.3);

    // Check boundaries
    if (turtle.y < 0) {
        turtle.y = 0;
        turtle.velocityY = 0;
    }

    if (turtle.y + turtle.height > canvas.height) {
        gameOver();
    }
}

function swim() {
    if (!gameRunning) return;

    if (!gameStarted) {
        gameStarted = true;
        // Resume audio context if needed (browser policy)
        if (audioContext && audioContext.state === 'suspended') {
            audioContext.resume();
        }
    }

    turtle.velocityY = turtle.swimPower;
    playSwimSound();
}

function spawnObstacle() {
    if (!gameStarted) return;

    if (frameCount % spawnRate === 0) {
        let minHeight = 60;
        let maxHeight = canvas.height - obstacleGap - 60;
        let topHeight = Math.random() * (maxHeight - minHeight) + minHeight;

        obstacles.push({
            x: canvas.width,
            topHeight: topHeight,
            gap: obstacleGap,
            passed: false
        });
    }
}

function updateObstacles() {
    for (let i = obstacles.length - 1; i >= 0; i--) {
        obstacles[i].x -= gameSpeed;

        // Check if turtle passed the obstacle (only if game is running)
        if (gameRunning && !obstacles[i].passed && obstacles[i].x + obstacleWidth < turtle.x) {
            obstacles[i].passed = true;
            score++;
            playScoreSound();
        }

        // Remove obstacles that are off screen
        if (obstacles[i].x + obstacleWidth < 0) {
            obstacles.splice(i, 1);
        }
    }
}

function updateBubbles() {
    for (let bubble of bubbles) {
        bubble.x -= bubble.speed;
        bubble.y -= bubble.speed * 0.5; // Bubbles rise

        if (bubble.x < -bubble.radius) {
            bubble.x = canvas.width + Math.random() * 100;
            bubble.y = canvas.height + Math.random() * 50;
            // Occasionally play bubble sound
            if (Math.random() < 0.1) {
                playBubbleSound();
            }
        }

        if (bubble.y < -bubble.radius) {
            bubble.y = canvas.height + Math.random() * 50;
        }
    }
}

function updateSeaweed() {
    for (let weed of seaweed) {
        weed.x -= weed.speed;
        weed.sway += 0.02;

        if (weed.x < -20) {
            weed.x = canvas.width + Math.random() * 100;
            weed.height = 80 + Math.random() * 60;
        }
    }
}

function checkCollisions() {
    if (!gameStarted) return;

    // Check obstacle collisions
    for (let obstacle of obstacles) {
        // Check collision with top coral
        if (turtle.x < obstacle.x + obstacleWidth &&
            turtle.x + turtle.width > obstacle.x &&
            turtle.y < obstacle.topHeight) {
            gameOver();
            return;
        }

        // Check collision with bottom coral
        if (turtle.x < obstacle.x + obstacleWidth &&
            turtle.x + turtle.width > obstacle.x &&
            turtle.y + turtle.height > obstacle.topHeight + obstacle.gap) {
            gameOver();
            return;
        }
    }
}

function gameOver() {
    gameRunning = false;
    gameStarted = false;
    finalScoreElement.textContent = score;
    gameOverElement.style.display = 'block';
    playGameOverSound();
}

function restartGame() {
    gameRunning = true;
    gameStarted = false;
    score = 0;
    frameCount = 0;

    // Reset turtle position
    turtle.x = 150;
    turtle.y = canvas.height / 2;
    turtle.velocityY = 0;
    turtle.rotation = 0;

    // Clear arrays
    obstacles = [];

    // Hide game over screen
    gameOverElement.style.display = 'none';
}

function updateScore() {
    scoreElement.textContent = score;
    finalScoreElement.textContent = score; // Keep final score in sync
}



function drawStartMessage() {
    if (!gameStarted && gameRunning) {
        ctx.fillStyle = 'rgba(0, 50, 100, 0.8)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = 'white';
        ctx.font = '36px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Swimming Turtle', canvas.width/2, canvas.height/2 - 50);

        ctx.font = '18px Arial';
        ctx.fillText('Click or press SPACE to swim up!', canvas.width/2, canvas.height/2 + 20);
        ctx.fillText('Navigate through the coral reefs!', canvas.width/2, canvas.height/2 + 45);
        ctx.textAlign = 'left';
    }
}



function gameLoop() {
    frameCount++;

    // Clear canvas with underwater gradient background
    let gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#4682B4');
    gradient.addColorStop(0.5, '#1E90FF');
    gradient.addColorStop(1, '#006994');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Update game objects
    updateTurtle();
    updateObstacles();
    updateBubbles();
    updateSeaweed();

    // Spawn new objects
    spawnObstacle();

    // Check collisions
    checkCollisions();

    // Draw background elements first
    for (let weed of seaweed) {
        drawSeaweed(weed);
    }

    for (let bubble of bubbles) {
        drawBubble(bubble);
    }

    // Draw obstacles
    for (let obstacle of obstacles) {
        drawObstacle(obstacle);
    }

    // Draw turtle on top
    drawTurtle();

    // Draw UI elements
    drawStartMessage();

    // Update score
    updateScore();

    requestAnimationFrame(gameLoop);
}

// Event listeners
document.addEventListener('keydown', function(event) {
    if (event.code === 'Space') {
        event.preventDefault();
        swim();
    }
});

canvas.addEventListener('click', function() {
    swim();
});

// Prevent context menu on right click
canvas.addEventListener('contextmenu', function(e) {
    e.preventDefault();
});

// Initialize and start game
initAudio();
initUnderwaterElements();
gameLoop();
