<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swimming Turtle</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1E90FF, #006994);
            font-family: Arial, sans-serif;
        }
        
        .game-container {
            text-align: center;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        canvas {
            border: 3px solid #333;
            border-radius: 10px;
            background: linear-gradient(to bottom, #4682B4 0%, #1E90FF 50%, #006994 100%);
        }
        
        .controls {
            margin-top: 15px;
            color: #333;
        }
        
        .score {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .instructions {
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            display: none;
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        
        button:hover {
            background: #2980b9;
        }

        .audio-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
        }

        .audio-toggle {
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .audio-toggle:hover {
            background: rgba(0,0,0,0.9);
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="audio-controls">
            <button class="audio-toggle" onclick="toggleAudio()" id="audioToggle">🔊 Sound ON</button>
        </div>
        <div class="score">Score: <span id="score">0</span></div>
        <canvas id="gameCanvas" width="800" height="400"></canvas>
        <div class="controls">
            <div class="instructions">
                Press SPACEBAR or click to make the turtle swim up!<br>
                Navigate through the coral reefs without touching them!
            </div>
        </div>
        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <button onclick="restartGame()">Play Again</button>
        </div>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
